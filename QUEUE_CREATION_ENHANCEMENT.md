# Azure Service Bus Queue Creation Enhancement

## Overview

The `sb_util.py` script has been enhanced to automatically create destination queues with the same settings as the source queues when they don't exist. This eliminates the need to manually create queues in the destination Service Bus namespace before running the message duplication process.

## New Features

### 1. Automatic Queue Creation
- **Function**: `create_queue_with_properties()`
- **Purpose**: Creates a new queue in the destination Service Bus with identical properties to the source queue
- **Properties Copied**:
  - Max size in megabytes
  - Requires duplicate detection
  - Requires session
  - Default message time to live
  - Dead lettering on message expiration
  - Duplicate detection history time window
  - Max delivery count
  - Enable batched operations
  - Auto delete on idle
  - Enable partitioning
  - Lock duration

### 2. Queue Properties Retrieval
- **Function**: `get_queue_properties()`
- **Purpose**: Retrieves all properties from a source queue using the ServiceBusAdministrationClient
- **Returns**: QueueProperties object containing all queue configuration settings

### 3. Enhanced Configuration
- **New Setting**: `AUTO_CREATE_QUEUES = True`
- **Purpose**: Controls whether to automatically create missing destination queues
- **Default**: Enabled (True)
- **Behavior**: When disabled, the script will skip queues that don't exist in the destination

## Technical Implementation

### New Dependencies
```python
from azure.servicebus.management import ServiceBusAdministrationClient, QueueProperties
```

### New Client Creation
The script now creates additional administration clients for queue management:
```python
source_admin_client = ServiceBusAdministrationClient.from_connection_string(SOURCE_CONNECTION_STR)
dest_admin_client = ServiceBusAdministrationClient.from_connection_string(DEST_CONNECTION_STR)
```

### Enhanced Queue Processing Flow
1. **Verify Source Queue**: Check if source queue exists and is accessible
2. **Get Source Properties**: Retrieve all configuration properties from source queue
3. **Check Destination Queue**: Verify if destination queue exists
4. **Create if Missing**: If destination queue doesn't exist and `AUTO_CREATE_QUEUES` is enabled:
   - Create new queue with identical properties to source
   - Log all copied properties for verification
   - Verify the newly created queue is accessible
5. **Proceed with Duplication**: Continue with message duplication process

## Error Handling

### Queue Creation Errors
- **Already Exists**: If queue already exists, logs a warning and continues
- **Creation Failed**: Logs error details and skips the queue
- **Permission Issues**: Provides detailed error messages for troubleshooting

### Connection Management
- Properly closes both regular and administration client connections
- Handles cleanup in finally block to ensure resources are released

## Configuration Options

### Queue Creation Control
```python
AUTO_CREATE_QUEUES = True  # Set to False to disable automatic queue creation
```

### Logging Enhancement
The script now logs:
- Configuration settings at startup
- Source queue properties before creation
- Destination queue properties after creation
- Creation success/failure status
- Property comparison details

## Usage Examples

### Basic Usage (Auto-create enabled)
```bash
python3 sb_util.py
```
- Script will automatically create missing destination queues
- All source queue properties will be copied to destination

### Manual Control (Auto-create disabled)
```python
# In sb_util.py, set:
AUTO_CREATE_QUEUES = False
```
- Script will skip queues that don't exist in destination
- Useful when you want to manually control queue creation

## Testing

### Test Script
A dedicated test script `test_queue_creation.py` is provided to:
- Test queue creation functionality without message duplication
- Compare source and destination queue properties
- Verify newly created queues are accessible
- Provide detailed logging of all queue properties

### Running Tests
```bash
python3 test_queue_creation.py
```

## Benefits

1. **Automation**: Eliminates manual queue creation steps
2. **Consistency**: Ensures destination queues have identical settings to source
3. **Error Reduction**: Reduces configuration mismatches between environments
4. **Flexibility**: Can be disabled if manual control is preferred
5. **Transparency**: Detailed logging shows exactly what properties are copied

## Requirements

The enhancement maintains the same requirements as the original script:
- `azure-servicebus>=7.11.0`
- Python 3.7+

The ServiceBusAdministrationClient is part of the same azure-servicebus package, so no additional dependencies are required.

## Backward Compatibility

The enhancement is fully backward compatible:
- Existing functionality remains unchanged
- New features are opt-in via configuration
- Original message duplication logic is preserved
- All existing configuration options work as before

## Security Considerations

- Uses the same connection strings as the original script
- Requires "Manage" permissions on the destination Service Bus namespace for queue creation
- Administration clients are properly closed to prevent connection leaks
- No additional credentials or permissions required beyond existing setup
