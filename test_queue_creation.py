#!/usr/bin/env python3
"""
Test script for Azure Service Bus Queue Creation functionality

This script tests the queue creation functionality without actually duplicating messages.
It will:
1. Connect to source and destination service buses
2. Get properties from source queues
3. Create destination queues with the same properties (if they don't exist)
4. Verify the queues were created successfully

Requirements:
- azure-servicebus>=7.11.0
- Python 3.7+
"""

from azure.servicebus import ServiceBusClient, TransportType
from azure.servicebus.management import ServiceBusAdministrationClient
import logging
from datetime import datetime
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'queue_creation_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

# Import functions from the main script
from sb_util import (
    SOURCE_CONNECTION_STR, DEST_CONNECTION_STR, QUEUES,
    parse_connection_string, test_connection, get_queue_properties,
    create_queue_with_properties, verify_queue_exists
)


def test_queue_creation():
    """Test the queue creation functionality."""
    start_time = datetime.now()
    logger.info("🧪 Starting Queue Creation Test")
    logger.info(f"⏰ Start time: {start_time}")
    logger.info(f"📋 Queues to test: {QUEUES}")

    # Parse connection strings for logging
    source_info = parse_connection_string(SOURCE_CONNECTION_STR)
    dest_info = parse_connection_string(DEST_CONNECTION_STR)

    logger.info(f"🔗 Source namespace: {source_info.get('namespace', 'unknown')}")
    logger.info(f"🔗 Destination namespace: {dest_info.get('namespace', 'unknown')}")

    successful_queues = 0
    failed_queues = 0

    try:
        # Create Service Bus clients
        logger.info("🔧 Creating Service Bus clients...")
        source_client = ServiceBusClient.from_connection_string(
            SOURCE_CONNECTION_STR,
            transport_type=TransportType.Amqp
        )
        dest_client = ServiceBusClient.from_connection_string(
            DEST_CONNECTION_STR,
            transport_type=TransportType.Amqp
        )

        # Create administration clients
        logger.info("🔧 Creating Service Bus administration clients...")
        source_admin_client = ServiceBusAdministrationClient.from_connection_string(SOURCE_CONNECTION_STR)
        dest_admin_client = ServiceBusAdministrationClient.from_connection_string(DEST_CONNECTION_STR)

        # Test connections
        source_connected = test_connection(source_client, "Source", SOURCE_CONNECTION_STR)
        dest_connected = test_connection(dest_client, "Destination", DEST_CONNECTION_STR)

        if not source_connected or not dest_connected:
            logger.error("❌ Connection tests failed. Aborting test.")
            return 1

        logger.info("✅ All Service Bus connections established successfully")

        # Test each queue
        for queue_name in QUEUES:
            logger.info(f"\n{'=' * 60}")
            logger.info(f"🧪 Testing queue: {queue_name}")
            logger.info(f"{'=' * 60}")

            try:
                # Verify source queue exists
                if not verify_queue_exists(source_client, queue_name, "source"):
                    logger.error(f"❌ Source queue '{queue_name}' does not exist. Skipping.")
                    failed_queues += 1
                    continue

                # Get source queue properties
                logger.info(f"📋 Getting source queue properties for '{queue_name}'...")
                source_queue_properties = get_queue_properties(source_admin_client, queue_name)
                if not source_queue_properties:
                    logger.error(f"❌ Failed to get source queue properties for '{queue_name}'. Skipping.")
                    failed_queues += 1
                    continue

                # Log source queue properties
                logger.info(f"📊 Source queue '{queue_name}' properties:")
                logger.info(f"   • Max size: {source_queue_properties.max_size_in_megabytes} MB")
                logger.info(f"   • Requires duplicate detection: {source_queue_properties.requires_duplicate_detection}")
                logger.info(f"   • Requires session: {source_queue_properties.requires_session}")
                logger.info(f"   • Default TTL: {source_queue_properties.default_message_time_to_live}")
                logger.info(f"   • Dead lettering on expiration: {source_queue_properties.dead_lettering_on_message_expiration}")
                logger.info(f"   • Max delivery count: {source_queue_properties.max_delivery_count}")
                logger.info(f"   • Enable batched operations: {source_queue_properties.enable_batched_operations}")
                logger.info(f"   • Enable partitioning: {source_queue_properties.enable_partitioning}")
                logger.info(f"   • Lock duration: {source_queue_properties.lock_duration}")
                logger.info(f"   • Enable express: {source_queue_properties.enable_express}")
                logger.info(f"   • Status: {source_queue_properties.status}")
                logger.info(f"   • Max message size: {source_queue_properties.max_message_size_in_kilobytes} KB")

                # Check if destination queue exists
                if verify_queue_exists(dest_client, queue_name, "destination"):
                    logger.info(f"✅ Destination queue '{queue_name}' already exists")
                    
                    # Get destination queue properties for comparison
                    dest_queue_properties = get_queue_properties(dest_admin_client, queue_name)
                    if dest_queue_properties:
                        logger.info(f"📊 Destination queue '{queue_name}' properties:")
                        logger.info(f"   • Max size: {dest_queue_properties.max_size_in_megabytes} MB")
                        logger.info(f"   • Requires duplicate detection: {dest_queue_properties.requires_duplicate_detection}")
                        logger.info(f"   • Requires session: {dest_queue_properties.requires_session}")
                        logger.info(f"   • Default TTL: {dest_queue_properties.default_message_time_to_live}")
                        logger.info(f"   • Dead lettering on expiration: {dest_queue_properties.dead_lettering_on_message_expiration}")
                        logger.info(f"   • Max delivery count: {dest_queue_properties.max_delivery_count}")
                        logger.info(f"   • Enable batched operations: {dest_queue_properties.enable_batched_operations}")
                        logger.info(f"   • Enable partitioning: {dest_queue_properties.enable_partitioning}")
                        logger.info(f"   • Lock duration: {dest_queue_properties.lock_duration}")
                        logger.info(f"   • Enable express: {dest_queue_properties.enable_express}")
                        logger.info(f"   • Status: {dest_queue_properties.status}")
                        logger.info(f"   • Max message size: {dest_queue_properties.max_message_size_in_kilobytes} KB")
                else:
                    # Create destination queue with source properties
                    logger.info(f"🏗️ Creating destination queue '{queue_name}' with source properties...")
                    if create_queue_with_properties(dest_admin_client, queue_name, source_queue_properties):
                        logger.info(f"✅ Successfully created destination queue '{queue_name}'")
                        
                        # Verify the created queue
                        if verify_queue_exists(dest_client, queue_name, "destination"):
                            logger.info(f"✅ Verified newly created queue '{queue_name}' is accessible")
                        else:
                            logger.warning(f"⚠️ Created queue '{queue_name}' but verification failed")
                    else:
                        logger.error(f"❌ Failed to create destination queue '{queue_name}'")
                        failed_queues += 1
                        continue

                successful_queues += 1
                logger.info(f"✅ Queue '{queue_name}' test completed successfully!")

            except Exception as e:
                logger.error(f"❌ Failed to test queue '{queue_name}': {e}")
                failed_queues += 1
                continue

        # Final summary
        end_time = datetime.now()
        duration = end_time - start_time

        logger.info(f"\n{'=' * 60}")
        logger.info(f"🎉 Queue Creation Test completed!")
        logger.info(f"{'=' * 60}")
        logger.info(f"⏰ Start time: {start_time}")
        logger.info(f"⏰ End time: {end_time}")
        logger.info(f"⏱️ Total duration: {duration}")
        logger.info(f"📊 Summary:")
        logger.info(f"   • Successful queues: {successful_queues}")
        logger.info(f"   • Failed queues: {failed_queues}")

        if failed_queues > 0:
            logger.warning(f"⚠️ {failed_queues} queue(s) failed to process. Check logs for details.")
            return 1

        logger.info("🎊 All queue creation tests completed successfully!")
        return 0

    except Exception as e:
        logger.error(f"💥 Critical error in test process: {e}")
        return 1

    finally:
        # Clean up connections
        try:
            if 'source_client' in locals():
                source_client.close()
                logger.info("🔌 Source client connection closed")
            if 'dest_client' in locals():
                dest_client.close()
                logger.info("🔌 Destination client connection closed")
            if 'source_admin_client' in locals():
                source_admin_client.close()
                logger.info("🔌 Source admin client connection closed")
            if 'dest_admin_client' in locals():
                dest_admin_client.close()
                logger.info("🔌 Destination admin client connection closed")
        except Exception as e:
            logger.warning(f"⚠️ Error closing connections: {e}")


if __name__ == "__main__":
    exit_code = test_queue_creation()
    exit(exit_code)
