#!/usr/bin/env python3
"""
Azure Service Bus Queue Message Duplicator (Enhanced Version)

This script duplicates messages from source Service Bus queues to destination Service Bus queues
using PEEK mode to ensure original messages remain untouched in the source queue.

Features:
- Uses peek mode to read messages without removing them from source
- Enhanced logging with connection status and detailed progress
- Preserves all message properties and metadata
- Handles both active messages and dead letter queue messages
- Batch processing for efficiency
- Retry logic for failed operations
- Automatic queue creation with same settings as source queue

Requirements:
- azure-servicebus>=7.11.0
- Python 3.7+

Environment Variables:
- SOURCE_SB_CONNECTION_STRING: Source Service Bus connection string
- DEST_SB_CONNECTION_STRING: Destination Service Bus connection string
"""

from azure.servicebus import ServiceBusClient, ServiceBusMessage, TransportType
from azure.servicebus.management import ServiceBusAdministrationClient, QueueProperties
import os
import logging
import time
from typing import List, Optional, Dict, Any
import re
from datetime import datetime

# Configure enhanced logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'servicebus_duplication_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

# Configuration - Use environment variables for security
SOURCE_CONNECTION_STR = "Endpoint=sb://wapol-wpm-sb-dev.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=as38I3tYmEKJjD53VUY3/yG7FUUI/Yi+2mwaaGahxdI="
DEST_CONNECTION_STR = "Endpoint=sb://wapol-wpm-sb-2-usr.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=i4tdu7oO12AS/3JjP2reAyFE0wecp608W+ASbPvGOZ4="


# Validate environment variables
if not SOURCE_CONNECTION_STR:
    raise ValueError("SOURCE_SB_CONNECTION_STRING environment variable is required")
if not DEST_CONNECTION_STR:
    raise ValueError("DEST_SB_CONNECTION_STRING environment variable is required")

# List of queues to migrate
QUEUES = [
    "core-user-persistence-dev_localtest",
    # Add more queues as needed
]

# Configuration options
BATCH_SIZE = 10  # Number of messages to process in each batch
MAX_RETRIES = 3  # Maximum number of retries for failed operations
RETRY_DELAY = 2  # Delay between retries in seconds
PEEK_BATCH_SIZE = 50  # Number of messages to peek at once
MAX_BATCH_SIZE_BYTES = 200000  # Maximum batch size in bytes (200KB, leaving buffer for Service Bus 256KB limit)
AUTO_CREATE_QUEUES = True  # Automatically create destination queues if they don't exist

# AMQP Configuration
AMQP_CONNECTION_TIMEOUT = 60  # Connection timeout in seconds
AMQP_IDLE_TIMEOUT = 300  # Idle timeout in seconds (5 minutes)


def parse_connection_string(connection_string: str) -> Dict[str, str]:
    """Parse connection string to extract namespace and other details."""
    result = {}
    pairs = connection_string.split(';')
    for pair in pairs:
        if '=' in pair:
            key, value = pair.split('=', 1)
            result[key] = value

    # Extract namespace from Endpoint
    if 'Endpoint' in result:
        endpoint = result['Endpoint']
        match = re.search(r'sb://([^/]+)\.servicebus\.windows\.net', endpoint)
        if match:
            result['namespace'] = match.group(1)

    return result


def test_connection(client: ServiceBusClient, connection_name: str, connection_string: str) -> bool:
    """Test Service Bus connection and log detailed status."""
    try:
        logger.info(f"🔗 Testing {connection_name} connection...")

        # Parse connection string for debugging
        conn_info = parse_connection_string(connection_string)
        logger.info(f"🔍 Namespace: {conn_info.get('namespace', 'unknown')}")
        logger.info(f"🔍 Endpoint: {conn_info.get('Endpoint', 'unknown')}")

        # Test connection using administration client (more reliable)
        try:
            admin_client = ServiceBusAdministrationClient.from_connection_string(connection_string)

            # Try to list queues - this is a lightweight operation that tests authentication
            try:
                queue_list = list(admin_client.list_queues())
                logger.info(f"✅ {connection_name} authentication successful")
                logger.info(f"🔍 Found {len(queue_list)} queues in namespace")
                admin_client.close()
                return True
            except Exception as list_error:
                admin_client.close()
                list_error_msg = str(list_error).lower()

                # Check if it's an authentication/authorization error
                if any(keyword in list_error_msg for keyword in ["auth", "token", "unauthorized", "forbidden", "access denied"]):
                    logger.error(f"❌ {connection_name} authentication/authorization failed: {list_error}")
                    logger.error(f"🔍 Check your connection string and permissions")
                    return False

                # For other errors, still consider connection successful if we can create admin client
                logger.warning(f"⚠️ {connection_name} list operation failed but connection seems valid: {list_error}")
                return True

        except Exception as admin_error:
            admin_error_msg = str(admin_error).lower()

            # Check for specific error types
            if any(keyword in admin_error_msg for keyword in ["auth", "token", "unauthorized"]):
                logger.error(f"❌ {connection_name} authentication failed: {admin_error}")
                logger.error(f"🔍 Check your connection string and permissions")
                return False

            if any(keyword in admin_error_msg for keyword in ["timeout", "network", "connection"]):
                logger.error(f"❌ {connection_name} network connection failed: {admin_error}")
                logger.error(f"🔍 Check network connectivity and firewall settings")
                return False

            # For other errors, log but don't fail immediately
            logger.warning(f"⚠️ {connection_name} admin client test failed: {admin_error}")

            # Fallback: try a simple client creation test
            try:
                # Just test if we can create the client without doing operations
                test_client = ServiceBusClient.from_connection_string(
                    connection_string,
                    transport_type=TransportType.Amqp
                )
                test_client.close()
                logger.info(f"✅ {connection_name} basic client creation successful")
                return True
            except Exception as client_error:
                logger.error(f"❌ {connection_name} basic client creation failed: {client_error}")
                return False

    except Exception as e:
        error_msg = str(e)
        logger.error(f"❌ {connection_name} connection test failed: {error_msg}")

        # Provide specific guidance based on error type
        if "token" in error_msg.lower() or "auth" in error_msg.lower():
            logger.error(f"🔍 Authentication issue detected:")
            logger.error(f"   • Check if your Shared Access Key is correct")
            logger.error(f"   • Verify the connection string format")
            logger.error(f"   • Ensure the Service Bus namespace exists")
            logger.error(f"   • Check if your IP is allowed (if IP restrictions are enabled)")

        if "timeout" in error_msg.lower() or "network" in error_msg.lower():
            logger.error(f"🔍 Network issue detected:")
            logger.error(f"   • Check firewall settings (ports 5671/5672)")
            logger.error(f"   • Verify network connectivity to Azure")
            logger.error(f"   • Check if corporate proxy is interfering")

        return False


def estimate_message_size(msg) -> int:
    """
    Estimate the size of a ServiceBusMessage in bytes.
    This includes body, properties, and metadata overhead.
    """
    try:
        size = 0

        # Message body size
        if msg.body:
            if hasattr(msg.body, '__iter__') and not isinstance(msg.body, (bytes, str)):
                size += len(b"".join(msg.body))
            else:
                size += len(msg.body) if isinstance(msg.body, (bytes, str)) else 0

        # Properties overhead (approximate)
        properties = [
            msg.content_type, msg.subject, msg.correlation_id,
            msg.message_id, msg.reply_to, msg.reply_to_session_id,
            msg.session_id
        ]

        for prop in properties:
            if prop:
                size += len(str(prop).encode('utf-8'))

        # Application properties
        if msg.application_properties:
            for key, value in msg.application_properties.items():
                if key:
                    size += len(str(key).encode('utf-8'))
                if value:
                    size += len(str(value).encode('utf-8'))

        # Add overhead for Service Bus metadata (approximate)
        size += 1024  # 1KB overhead for headers and metadata

        return size

    except Exception as e:
        logger.warning(f"⚠️ Could not estimate message size: {e}")
        return 10240  # Default to 10KB if estimation fails


def create_duplicate_message(original_msg) -> ServiceBusMessage:
    """
    Create a duplicate ServiceBusMessage from an original message,
    preserving all properties and metadata.
    """
    try:
        # Handle message body properly
        if hasattr(original_msg.body, '__iter__') and not isinstance(original_msg.body, (bytes, str)):
            body = b"".join(original_msg.body)
        else:
            body = original_msg.body

        # Create new message with all properties
        new_msg = ServiceBusMessage(
            body=body,
            content_type=original_msg.content_type,
            subject=original_msg.subject,
            correlation_id=original_msg.correlation_id,
            message_id=original_msg.message_id,
            reply_to=original_msg.reply_to,
            reply_to_session_id=original_msg.reply_to_session_id,
            session_id=original_msg.session_id,
            time_to_live=original_msg.time_to_live
        )

        # Copy application properties - handle None case properly
        if original_msg.application_properties:
            # Ensure application_properties is initialized as a dict if it's None
            if new_msg.application_properties is None:
                new_msg.application_properties = {}

            for key, value in original_msg.application_properties.items():
                new_msg.application_properties[key] = value

            logger.debug(f"📋 Copied {len(original_msg.application_properties)} application properties")

        body_size = len(body) if body else 0
        logger.debug(f"📋 Message duplicated - ID: {original_msg.message_id}, Size: {body_size} bytes")
        return new_msg

    except Exception as e:
        logger.error(f"❌ Error creating duplicate message: {e}")
        logger.error(f"❌ Original message ID: {getattr(original_msg, 'message_id', 'unknown')}")
        logger.error(f"❌ Original message properties: {getattr(original_msg, 'application_properties', 'None')}")
        raise


def send_message_batch(sender, messages: List[ServiceBusMessage], batch_size_bytes: int) -> bool:
    """
    Send a batch of messages with retry logic and size validation.

    Args:
        sender: ServiceBus sender client
        messages: List of messages to send
        batch_size_bytes: Estimated size of the batch in bytes

    Returns:
        True if successful, False otherwise
    """
    if not messages:
        return True

    logger.debug(f"📤 Attempting to send batch of {len(messages)} messages ({batch_size_bytes:,} bytes)")

    for retry in range(MAX_RETRIES):
        try:
            sender.send_messages(messages)
            logger.debug(f"✅ Batch sent successfully on attempt {retry + 1}")
            return True

        except Exception as e:
            error_msg = str(e)

            # Check if it's a size limit error
            if "size limit" in error_msg.lower() or "262144" in error_msg:
                logger.warning(f"⚠️ Batch size limit exceeded ({batch_size_bytes:,} bytes). Splitting batch...")

                # Split the batch in half and try sending each part
                if len(messages) > 1:
                    mid = len(messages) // 2
                    first_half = messages[:mid]
                    second_half = messages[mid:]

                    # Estimate sizes for each half
                    first_half_size = batch_size_bytes // 2
                    second_half_size = batch_size_bytes - first_half_size

                    logger.info(f"🔄 Splitting batch: {len(first_half)} + {len(second_half)} messages")

                    # Send each half recursively
                    success1 = send_message_batch(sender, first_half, first_half_size)
                    success2 = send_message_batch(sender, second_half, second_half_size)

                    return success1 and success2
                else:
                    # Single message is too large
                    logger.error(f"❌ Single message exceeds Service Bus size limit: {batch_size_bytes:,} bytes")
                    return False

            # Other errors - retry with delay
            if retry < MAX_RETRIES - 1:
                logger.warning(f"⚠️ Retry {retry + 1}/{MAX_RETRIES} for sending batch: {error_msg}")
                time.sleep(RETRY_DELAY)
            else:
                logger.error(f"❌ Failed to send batch after {MAX_RETRIES} retries: {error_msg}")
                return False

    return False


def get_queue_properties(admin_client: ServiceBusAdministrationClient, queue_name: str) -> Optional[QueueProperties]:
    """Get queue properties from the source service bus."""
    try:
        logger.info(f"🔍 Getting properties for queue '{queue_name}'...")
        queue_properties = admin_client.get_queue(queue_name)
        logger.info(f"✅ Successfully retrieved properties for queue '{queue_name}'")
        return queue_properties
    except Exception as e:
        logger.error(f"❌ Failed to get properties for queue '{queue_name}': {e}")
        return None


def create_queue_with_properties(admin_client: ServiceBusAdministrationClient, queue_name: str,
                                source_properties: QueueProperties) -> bool:
    """Create a queue in the destination service bus with the same properties as the source."""
    try:
        logger.info(f"🏗️ Creating queue '{queue_name}' with source properties...")

        # Create the queue using keyword arguments approach (more reliable)
        created_queue = admin_client.create_queue(
            queue_name=queue_name,
            max_size_in_megabytes=source_properties.max_size_in_megabytes,
            requires_duplicate_detection=source_properties.requires_duplicate_detection,
            requires_session=source_properties.requires_session,
            default_message_time_to_live=source_properties.default_message_time_to_live,
            dead_lettering_on_message_expiration=source_properties.dead_lettering_on_message_expiration,
            duplicate_detection_history_time_window=source_properties.duplicate_detection_history_time_window,
            max_delivery_count=source_properties.max_delivery_count,
            enable_batched_operations=source_properties.enable_batched_operations,
            auto_delete_on_idle=source_properties.auto_delete_on_idle,
            enable_partitioning=source_properties.enable_partitioning,
            lock_duration=source_properties.lock_duration,
            # Additional parameters
            authorization_rules=source_properties.authorization_rules,
            enable_express=source_properties.enable_express,
            forward_to=source_properties.forward_to,
            user_metadata=source_properties.user_metadata,
            forward_dead_lettered_messages_to=source_properties.forward_dead_lettered_messages_to,
            max_message_size_in_kilobytes=source_properties.max_message_size_in_kilobytes
        )

        logger.info(f"✅ Successfully created queue '{queue_name}' with properties:")
        logger.info(f"   • Max size: {created_queue.max_size_in_megabytes} MB")
        logger.info(f"   • Requires duplicate detection: {created_queue.requires_duplicate_detection}")
        logger.info(f"   • Requires session: {created_queue.requires_session}")
        logger.info(f"   • Default TTL: {created_queue.default_message_time_to_live}")
        logger.info(f"   • Dead lettering on expiration: {created_queue.dead_lettering_on_message_expiration}")
        logger.info(f"   • Max delivery count: {created_queue.max_delivery_count}")
        logger.info(f"   • Enable batched operations: {created_queue.enable_batched_operations}")
        logger.info(f"   • Enable partitioning: {created_queue.enable_partitioning}")
        logger.info(f"   • Lock duration: {created_queue.lock_duration}")
        logger.info(f"   • Enable express: {created_queue.enable_express}")
        logger.info(f"   • Status: {created_queue.status}")
        logger.info(f"   • Max message size: {created_queue.max_message_size_in_kilobytes} KB")

        return True

    except Exception as e:
        error_msg = str(e)
        if "already exists" in error_msg.lower() or "conflict" in error_msg.lower():
            logger.warning(f"⚠️ Queue '{queue_name}' already exists in destination")
            return True
        else:
            logger.error(f"❌ Failed to create queue '{queue_name}': {e}")
            return False


def verify_queue_exists(client: ServiceBusClient, queue_name: str, connection_name: str) -> bool:
    """Verify that a queue exists and is accessible."""
    try:
        logger.info(f"🔍 Verifying queue '{queue_name}' exists in {connection_name}...")

        # Test by creating a receiver (this will fail if queue doesn't exist)
        with client.get_queue_receiver(queue_name) as receiver:
            logger.info(f"✅ Queue '{queue_name}' verified in {connection_name}")
            return True

    except Exception as e:
        logger.error(f"❌ Queue '{queue_name}' verification failed in {connection_name}: {e}")
        return False


def duplicate_queue_messages(queue_name: str, source_client: ServiceBusClient,
                             dest_client: ServiceBusClient, is_dlq: bool = False) -> int:
    """
    Duplicate messages from source queue to destination queue using PEEK mode.

    Args:
        queue_name: Name of the queue
        source_client: Source Service Bus client
        dest_client: Destination Service Bus client
        is_dlq: Whether to process dead letter queue messages

    Returns:
        Number of messages duplicated
    """
    queue_type = "Dead Letter Queue" if is_dlq else "Active Queue"
    logger.info(f"🔄 Starting PEEK mode duplication from {queue_type}: {queue_name}")

    total_duplicated = 0

    try:
        # Create receiver for source queue (no receive mode needed for peek)
        source_receiver = source_client.get_queue_receiver(
            queue_name=queue_name,
            sub_queue="deadletter" if is_dlq else None
        )

        # Create sender for destination queue
        dest_sender = dest_client.get_queue_sender(queue_name=queue_name)

        logger.info(f"🔗 Connected to source {queue_type}: {queue_name}")
        logger.info(f"🔗 Connected to destination queue: {queue_name}")

        with source_receiver, dest_sender:
            sequence_number = 0
            batch_count = 0

            while True:
                try:
                    batch_count += 1
                    logger.info(f"📦 Processing batch {batch_count} (starting from sequence {sequence_number})")

                    # PEEK messages from source (this doesn't remove them)
                    messages = source_receiver.peek_messages(
                        max_message_count=PEEK_BATCH_SIZE,
                        sequence_number=sequence_number
                    )

                    if not messages:
                        logger.info(f"✅ No more messages found in {queue_type}: {queue_name}")
                        break

                    logger.info(f"👀 Peeked {len(messages)} messages from {queue_type}")

                    # Process messages with smart batching based on size
                    batch_messages = []
                    current_batch_size = 0
                    messages_processed = 0

                    for msg in messages:
                        try:
                            # Update sequence number for next iteration
                            sequence_number = max(sequence_number, msg.sequence_number + 1)

                            # Log message details for debugging
                            msg_id = getattr(msg, 'message_id', 'unknown')
                            msg_size = estimate_message_size(msg)
                            logger.debug(
                                f"🔍 Processing message - ID: {msg_id}, Seq: {msg.sequence_number}, Size: {msg_size} bytes")

                            # Check if adding this message would exceed batch size limit
                            if (current_batch_size + msg_size > MAX_BATCH_SIZE_BYTES and batch_messages) or len(
                                    batch_messages) >= BATCH_SIZE:
                                # Send current batch before adding this message
                                if batch_messages:
                                    success = send_message_batch(dest_sender, batch_messages, current_batch_size)
                                    if success:
                                        total_duplicated += len(batch_messages)
                                        logger.info(
                                            f"✅ Successfully duplicated {len(batch_messages)} messages ({current_batch_size:,} bytes)")
                                    else:
                                        logger.error(f"❌ Failed to send batch of {len(batch_messages)} messages")
                                        break

                                # Reset batch
                                batch_messages = []
                                current_batch_size = 0

                            # Handle oversized single messages
                            if msg_size > MAX_BATCH_SIZE_BYTES:
                                logger.warning(f"⚠️ Large message detected - ID: {msg_id}, Size: {msg_size:,} bytes")
                                logger.info(f"📤 Sending large message individually...")

                                # Create duplicate message
                                duplicate_msg = create_duplicate_message(msg)

                                # Send single large message
                                success = send_message_batch(dest_sender, [duplicate_msg], msg_size)
                                if success:
                                    total_duplicated += 1
                                    logger.info(f"✅ Successfully duplicated large message - ID: {msg_id}")
                                else:
                                    logger.error(f"❌ Failed to send large message - ID: {msg_id}")

                                messages_processed += 1
                                continue

                            # Create duplicate message and add to batch
                            duplicate_msg = create_duplicate_message(msg)
                            batch_messages.append(duplicate_msg)
                            current_batch_size += msg_size
                            messages_processed += 1

                            logger.debug(
                                f"📋 Added to batch - ID: {msg_id}, Batch size: {len(batch_messages)}, Total bytes: {current_batch_size:,}")

                        except Exception as e:
                            msg_id = getattr(msg, 'message_id', 'unknown')
                            logger.error(f"❌ Error processing message {msg_id}: {e}")
                            logger.error(f"❌ Message details - Seq: {getattr(msg, 'sequence_number', 'unknown')}")
                            continue

                    # Send remaining messages in batch
                    if batch_messages:
                        success = send_message_batch(dest_sender, batch_messages, current_batch_size)
                        if success:
                            total_duplicated += len(batch_messages)
                            logger.info(
                                f"✅ Successfully duplicated final batch of {len(batch_messages)} messages ({current_batch_size:,} bytes)")
                        else:
                            logger.error(f"❌ Failed to send final batch of {len(batch_messages)} messages")

                    logger.info(f"📊 Processed {messages_processed} messages in this peek batch")

                    # Small delay to avoid overwhelming the service
                    time.sleep(0.1)

                except Exception as e:
                    logger.error(f"❌ Error processing batch in {queue_type} {queue_name}: {e}")
                    break

    except Exception as e:
        logger.error(f"❌ Error setting up clients for {queue_type} {queue_name}: {e}")
        raise

    logger.info(f"🎯 Completed PEEK mode duplication for {queue_type}: {queue_name}")
    logger.info(f"📊 Total messages duplicated: {total_duplicated}")
    logger.info(f"✅ All original messages remain in source {queue_type}")

    return total_duplicated


def main():
    """Main function to orchestrate the queue duplication process."""
    start_time = datetime.now()
    logger.info("🚀 Starting Azure Service Bus Queue Duplication (PEEK Mode)")
    logger.info(f"⏰ Start time: {start_time}")
    logger.info(f"📋 Queues to process: {QUEUES}")
    logger.info(f"🔧 Configuration:")
    logger.info(f"   • Auto-create queues: {AUTO_CREATE_QUEUES}")
    logger.info(f"   • Batch size: {BATCH_SIZE}")
    logger.info(f"   • Peek batch size: {PEEK_BATCH_SIZE}")
    logger.info(f"   • Max batch size: {MAX_BATCH_SIZE_BYTES:,} bytes")

    # Parse connection strings for logging
    source_info = parse_connection_string(SOURCE_CONNECTION_STR)
    dest_info = parse_connection_string(DEST_CONNECTION_STR)

    logger.info(f"🔗 Source namespace: {source_info.get('namespace', 'unknown')}")
    logger.info(f"🔗 Destination namespace: {dest_info.get('namespace', 'unknown')}")

    total_messages_duplicated = 0
    successful_queues = 0
    failed_queues = 0

    try:
        # Create Service Bus clients with AMQP transport
        logger.info("🔧 Creating Service Bus clients with AMQP transport...")
        logger.info("🔗 Using AMQP over TCP for better performance and reliability")

        source_client = ServiceBusClient.from_connection_string(
            SOURCE_CONNECTION_STR,
            transport_type=TransportType.Amqp,
            connection_timeout=AMQP_CONNECTION_TIMEOUT,
            idle_timeout=AMQP_IDLE_TIMEOUT
        )
        dest_client = ServiceBusClient.from_connection_string(
            DEST_CONNECTION_STR,
            transport_type=TransportType.Amqp,
            connection_timeout=AMQP_CONNECTION_TIMEOUT,
            idle_timeout=AMQP_IDLE_TIMEOUT
        )

        # Create Service Bus administration clients for queue management
        logger.info("🔧 Creating Service Bus administration clients...")
        source_admin_client = ServiceBusAdministrationClient.from_connection_string(SOURCE_CONNECTION_STR)
        dest_admin_client = ServiceBusAdministrationClient.from_connection_string(DEST_CONNECTION_STR)

        logger.info(f"🔧 AMQP Configuration:")
        logger.info(f"   • Connection timeout: {AMQP_CONNECTION_TIMEOUT}s")
        logger.info(f"   • Idle timeout: {AMQP_IDLE_TIMEOUT}s")
        logger.info(f"   • Transport: AMQP over TCP")

        # Test connections
        source_connected = test_connection(source_client, "Source", SOURCE_CONNECTION_STR)
        dest_connected = test_connection(dest_client, "Destination", DEST_CONNECTION_STR)

        if not source_connected or not dest_connected:
            logger.error("❌ Connection tests failed. Aborting operation.")
            return 1

        logger.info("✅ All Service Bus connections established successfully")

        # Process each queue
        for queue_name in QUEUES:
            logger.info(f"\n{'=' * 60}")
            logger.info(f"📦 Processing queue: {queue_name}")
            logger.info(f"{'=' * 60}")

            try:
                # Verify source queue exists and get its properties
                if not verify_queue_exists(source_client, queue_name, "source"):
                    logger.error(f"❌ Source queue '{queue_name}' does not exist. Skipping.")
                    failed_queues += 1
                    continue

                # Get source queue properties
                logger.info(f"📋 Getting source queue properties for '{queue_name}'...")
                source_queue_properties = get_queue_properties(source_admin_client, queue_name)
                if not source_queue_properties:
                    logger.error(f"❌ Failed to get source queue properties for '{queue_name}'. Skipping.")
                    failed_queues += 1
                    continue

                # Check if destination queue exists, if not create it with source properties
                if not verify_queue_exists(dest_client, queue_name, "destination"):
                    if AUTO_CREATE_QUEUES:
                        logger.info(f"🏗️ Destination queue '{queue_name}' does not exist. Creating with source properties...")
                        if not create_queue_with_properties(dest_admin_client, queue_name, source_queue_properties):
                            logger.error(f"❌ Failed to create destination queue '{queue_name}'. Skipping.")
                            failed_queues += 1
                            continue
                        logger.info(f"✅ Successfully created destination queue '{queue_name}'")
                    else:
                        logger.error(f"❌ Destination queue '{queue_name}' does not exist and AUTO_CREATE_QUEUES is disabled. Skipping.")
                        failed_queues += 1
                        continue
                else:
                    logger.info(f"✅ Destination queue '{queue_name}' already exists")

                # Duplicate active messages
                logger.info(f"🔄 Starting active messages duplication for: {queue_name}")
                active_count = duplicate_queue_messages(
                    queue_name, source_client, dest_client, is_dlq=False
                )

                # Duplicate dead letter queue messages
                logger.info(f"🔄 Starting dead letter messages duplication for: {queue_name}")
                dlq_count = duplicate_queue_messages(
                    queue_name, source_client, dest_client, is_dlq=True
                )

                queue_total = active_count + dlq_count
                total_messages_duplicated += queue_total
                successful_queues += 1

                logger.info(f"✅ Queue '{queue_name}' completed successfully!")
                logger.info(f"📊 Active messages: {active_count}")
                logger.info(f"📊 Dead letter messages: {dlq_count}")
                logger.info(f"📊 Total for this queue: {queue_total}")

            except Exception as e:
                logger.error(f"❌ Failed to process queue '{queue_name}': {e}")
                failed_queues += 1
                continue

        # Final summary
        end_time = datetime.now()
        duration = end_time - start_time

        logger.info(f"\n{'=' * 60}")
        logger.info(f"🎉 Duplication process completed!")
        logger.info(f"{'=' * 60}")
        logger.info(f"⏰ Start time: {start_time}")
        logger.info(f"⏰ End time: {end_time}")
        logger.info(f"⏱️ Total duration: {duration}")
        logger.info(f"📊 Summary:")
        logger.info(f"   • Total messages duplicated: {total_messages_duplicated}")
        logger.info(f"   • Successful queues: {successful_queues}")
        logger.info(f"   • Failed queues: {failed_queues}")
        logger.info(f"   • Source messages remain untouched (PEEK mode used)")

        if failed_queues > 0:
            logger.warning(f"⚠️ {failed_queues} queue(s) failed to process. Check logs for details.")
            return 1

        logger.info("🎊 All operations completed successfully!")
        return 0

    except Exception as e:
        logger.error(f"💥 Critical error in main process: {e}")
        return 1

    finally:
        # Clean up connections
        try:
            if 'source_client' in locals():
                source_client.close()
                logger.info("🔌 Source client connection closed")
            if 'dest_client' in locals():
                dest_client.close()
                logger.info("🔌 Destination client connection closed")
            if 'source_admin_client' in locals():
                source_admin_client.close()
                logger.info("🔌 Source admin client connection closed")
            if 'dest_admin_client' in locals():
                dest_admin_client.close()
                logger.info("🔌 Destination admin client connection closed")
        except Exception as e:
            logger.warning(f"⚠️ Error closing connections: {e}")


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
